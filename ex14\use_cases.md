# Use Cases for Book Club Management System

---

### **Use Case 1: Member Views Current Reading Progress**

*   **Use Case Name:** View Current Reading Progress
*   **Actor:** Member
*   **Pre-conditions:**
    *   The user is logged into the system.
    *   The club has selected a book to read.
*   **Primary Flow:**
    1.  The user navigates to the main dashboard.
    2.  The system displays the current book's title, author, and cover.
    3.  The system shows a progress bar indicating the club's overall reading progress.
    4.  The system lists upcoming meeting dates related to the current book.
*   **Post-conditions:**
    *   The user is informed about the current reading status and schedule.

---

### **Use Case 2: Member Votes for the Next Book**

*   **Use Case Name:** Vote for Next Book
*   **Actor:** Member
*   **Pre-conditions:**
    *   The user is logged into the system.
    *   The voting period for the next book is open.
    *   There is a list of suggested books available for voting.
*   **Primary Flow:**
    1.  The user navigates to the "Vote for Next Book" section.
    2.  The system displays the list of suggested books with their titles and authors.
    3.  The user selects one book from the list.
    4.  The user clicks the "Submit Vote" button.
    5.  The system records the vote and shows a confirmation message.
*   **Post-conditions:**
    *   The user's vote is successfully cast and stored in the system.
*   **Alternate Flow:**
    *   **4a. User has already voted:** If the user tries to vote again, the system displays a message saying, "You have already voted."

---

### **Use Case 3: Member Suggests a New Book**

*   **Use Case Name:** Suggest a New Book
*   **Actor:** Member
*   **Pre-conditions:**
    *   The user is logged into the system.
*   **Primary Flow:**
    1.  The user navigates to the "Suggest a Book" page.
    2.  The system displays a form with fields for "Book Title" and "Author".
    3.  The user fills in the book details.
    4.  The user clicks the "Submit Suggestion" button.
    5.  The system adds the book to the list of suggestions for the next voting period.
    6.  The system displays a success message.
*   **Post-conditions:**
    *   The new book suggestion is added to the system.
*   **Alternate Flow:**
    *   **5a. Book already exists:** If the suggested book is already on the suggestion list, the system displays an error message, "This book has already been suggested."