Sub CreateMockupSlides()
    ' This macro creates mock-up slides for the Book Club Management System.

    ' Add Slide 1: Dashboard
    Call AddDashboardSlide

    ' Add Slide 2: Vote for Next Book
    Call AddVotingSlide

    ' Add Slide 3: Suggest a New Book
    Call AddSuggestionSlide
End Sub

Sub AddDashboardSlide()
    Dim sld As Slide
    Dim shp As Shape

    ' Add a new slide
    Set sld = ActivePresentation.Slides.Add(ActivePresentation.Slides.Count + 1, ppLayoutTitleOnly)
    sld.Shapes.Title.TextFrame.TextRange.Text = "Dashboard: Current Book"

    ' Add "Currently Reading" section
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 100, 620, 50)
    shp.TextFrame.TextRange.Text = "Currently Reading: 'The Midnight Library' by Matt <PERSON>g"
    shp.TextFrame.TextRange.Font.Bold = msoTrue
    shp.TextFrame.TextRange.Font.Size = 24

    ' Add Progress Bar
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 160, 200, 30)
    shp.TextFrame.TextRange.Text = "Club Progress: 75%"
    Set shp = sld.Shapes.AddShape(msoShapeRectangle, 50, 190, 620, 30)
    shp.Fill.ForeColor.RGB = RGB(200, 200, 200)
    Set shp = sld.Shapes.AddShape(msoShapeRectangle, 50, 190, 620 * 0.75, 30)
    shp.Fill.ForeColor.RGB = RGB(0, 176, 80)

    ' Add "Upcoming Meetings" section
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 250, 620, 50)
    shp.TextFrame.TextRange.Text = "Upcoming Meetings"
    shp.TextFrame.TextRange.Font.Bold = msoTrue
    shp.TextFrame.TextRange.Font.Size = 20

    ' Add Meeting 1
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 300, 620, 50)
    shp.TextFrame.TextRange.Text = "- June 15, 2025, 7:00 PM: Discussion on Chapters 1-10"
    
    ' Add Meeting 2
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 340, 620, 50)
    shp.TextFrame.TextRange.Text = "- June 29, 2025, 7:00 PM: Final Review & Next Book Announcement"
End Sub

Sub AddVotingSlide()
    Dim sld As Slide
    Dim shp As Shape

    ' Add a new slide
    Set sld = ActivePresentation.Slides.Add(ActivePresentation.Slides.Count + 1, ppLayoutTitleOnly)
    sld.Shapes.Title.TextFrame.TextRange.Text = "Vote for the Next Book"

    ' Add instruction text
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 100, 620, 30)
    shp.TextFrame.TextRange.Text = "Select one book from the list below and click 'Submit Vote'."
    shp.TextFrame.TextRange.Font.Size = 18

    ' Add Book 1
    Set shp = sld.Shapes.AddShape(msoShapeFlowchartProcess, 50, 150, 620, 40)
    shp.TextFrame.TextRange.Text = "Project Hail Mary by Andy Weir"
    
    ' Add Book 2
    Set shp = sld.Shapes.AddShape(msoShapeFlowchartProcess, 50, 200, 620, 40)
    shp.TextFrame.TextRange.Text = "Klara and the Sun by Kazuo Ishiguro"
    
    ' Add Book 3
    Set shp = sld.Shapes.AddShape(msoShapeFlowchartProcess, 50, 250, 620, 40)
    shp.TextFrame.TextRange.Text = "The Four Winds by Kristin Hannah"

    ' Add "Submit Vote" button
    Set shp = sld.Shapes.AddShape(msoShapeRectangle, 280, 320, 160, 50)
    shp.TextFrame.TextRange.Text = "Submit Vote"
    shp.Fill.ForeColor.RGB = RGB(79, 129, 189)
    shp.TextFrame.TextRange.Font.Color.RGB = RGB(255, 255, 255)
End Sub

Sub AddSuggestionSlide()
    Dim sld As Slide
    Dim shp As Shape

    ' Add a new slide
    Set sld = ActivePresentation.Slides.Add(ActivePresentation.Slides.Count + 1, ppLayoutTitleOnly)
    sld.Shapes.Title.TextFrame.TextRange.Text = "Suggest a New Book"

    ' Add "Book Title" label and textbox
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 120, 150, 30)
    shp.TextFrame.TextRange.Text = "Book Title:"
    shp.TextFrame.TextRange.Font.Bold = msoTrue
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 200, 120, 470, 40)
    shp.TextFrame.TextRange.Text = "Enter title here..."
    shp.Fill.ForeColor.RGB = RGB(240, 240, 240)

    ' Add "Author" label and textbox
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 180, 150, 30)
    shp.TextFrame.TextRange.Text = "Author:"
    shp.TextFrame.TextRange.Font.Bold = msoTrue
    Set shp = sld.Shapes.AddTextbox(msoTextOrientationHorizontal, 200, 180, 470, 40)
    shp.TextFrame.TextRange.Text = "Enter author's name here..."
    shp.Fill.ForeColor.RGB = RGB(240, 240, 240)

    ' Add "Submit Suggestion" button
    Set shp = sld.Shapes.AddShape(msoShapeRectangle, 280, 250, 200, 50)
    shp.TextFrame.TextRange.Text = "Submit Suggestion"
    shp.Fill.ForeColor.RGB = RGB(79, 129, 189)
    shp.TextFrame.TextRange.Font.Color.RGB = RGB(255, 255, 255)
End Sub