# Software Testing: Final Project
## [14] Exercise: Proposing a System

**Hirohisa AMAN (阿萬 裕久)**
<EMAIL>

---

## Purpose of the Exercise

*   Propose a simple web application and consider its use cases.
*   By considering use cases, you will in turn consider test cases for system testing.

**Main Contents**
*   Proposal of a web application
*   Creation of a mock-up
*   Creation of use cases

---

## Exercise Contents

### Task

*   Think of a simple web application and create a document explaining its functionality.
*   Create examples of the application's screens (mock-ups).
    (Draw the figures in PowerPoint)
*   Write the application's use cases.

---

## Assignment Submission

*   Submit your application description (Word file), mock-up (PowerPoint file), and use case descriptions (Word file) via Teams.