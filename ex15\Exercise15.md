# Software Testing: Final Project
## [15] Exercise: Testing of Proposed System (designing test cases)

Hirohisa AMAN
<EMAIL>

---

## Purpose of the Exercise

*   Improve the completeness of your proposed Web application by having it reviewed by another person.
*   Create test cases based on use cases.
*   Main Contents:
    *   Review of the proposed Web application.
    *   Improvement of the proposal contents and use cases.
    *   Creation of test cases.

---

## Content of the Exercise

### Task
*   Explain your proposed Web application and use cases to another person and get feedback.
    *   Add or modify use cases as needed.
*   Write the test cases corresponding to your use cases.

---

## Test Cases Corresponding to Use Cases

*   Write test cases as scenarios, assuming the actual operation of the system according to the use case.
*   In other words, provide **concrete examples** for everything that appears in the use case:
    *   The actor (user, etc.)
    *   Input strings and numbers
    *   Content to be displayed

---

## Assignment Submission

*   Submit your final version of your Web application proposal (including the description, mock-up, and use cases) and the test cases via Teams.